package org.dromara.itsm.util.prometheus;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.itsm.util.prometheus.dto.MetricDTO;
import org.dromara.itsm.util.prometheus.dto.RespDTO;
import org.dromara.itsm.util.prometheus.dto.ResultDTO;
import org.dromara.itsm.util.prometheus.dto.SystemInfoDTO;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 *      通过 Prometheus api 接口获取性能指标
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/19 15:06
 */
@Slf4j
public class PrometheusApiUtils {
    private static String PROMETHEUS_QUERY_API_URL;
    private static String PROMETHEUS_QUERY_RANGE_API_URL;


    // 在 PrometheusApiUtils 类顶部添加以下静态变量
    private static String NETWORK_DEVICE = ""; // 初始为空，首次使用时自动检测
    private static final Set<String> VIRTUAL_DEVICE_PREFIXES = Set.of("lo", "veth", "docker", "br-", "tun");
    private static final long DEVICE_CACHE_TIMEOUT_MS = 3600000; // 1小时缓存
    private static volatile long lastDeviceDetectionTime = 0;


    private static final Set<String> COMMON_DISK_PREFIXES = Set.of("sd", "vd", "xvd", "nvme", "hd", "emcpower", "dm-");
    private static String DISK_DEVICE = ""; // 缓存检测到的磁盘设备
    private static final long DISK_CACHE_TIMEOUT_MS = 3600000; // 1小时缓存
    private static volatile long lastDiskDetectionTime = 0;

    // 添加以下静态方法
    public static void setNetworkDevice(String device) {
        NETWORK_DEVICE = device;
        lastDeviceDetectionTime = System.currentTimeMillis();
        log.info("已手动设置监控网卡设备为: {}", device);
    }

    public static String getPrometheusQueryApiUrl() {
        return PROMETHEUS_QUERY_API_URL;
    }

    public static void setPrometheusQueryApiUrl(String prometheusQueryApiUrl) {
        PROMETHEUS_QUERY_API_URL = prometheusQueryApiUrl;
    }

    public static String getPrometheusQueryRangeApiUrl() {
        return PROMETHEUS_QUERY_RANGE_API_URL;
    }

    public static void setPrometheusQueryRangeApiUrl(String prometheusQueryRangeApiUrl) {
        PROMETHEUS_QUERY_RANGE_API_URL = prometheusQueryRangeApiUrl;
    }

    public static String post(String query) {
        log.info("PrometheusApiUtils:post -> [query参数] {}", query);
        HttpResponse execute = HttpUtil.createPost(PROMETHEUS_QUERY_API_URL).form("query", query).execute();
        String body = execute.body();
        log.info("PrometheusApiUtils:post -> [返回结果] {}", body);
        return body;
    }

    public static String queryPost(Map<String, Object> reqParams) {
        log.info("queryPost ->[reqParams参数] {}", reqParams);
        HttpResponse execute = HttpUtil.createPost(PROMETHEUS_QUERY_API_URL).form(reqParams).execute();
//        HttpResponse execute = HttpUtil.createPost("http://172.29.95.234:9090/prometheus/api/v1/query").form(reqParams).execute();
        String body = execute.body();
        log.info("queryPost -> [返回结果] {}", body);
        return body;
    }

    public static String  queryRangePost(Map<String, Object> reqParams) {
        log.info("queryRangePost ->[reqParams参数] {}", reqParams);
        HttpResponse execute = HttpUtil.createPost(PROMETHEUS_QUERY_RANGE_API_URL).form(reqParams).execute();
//        HttpResponse execute = HttpUtil.createPost("http://172.29.95.234:9090/prometheus/api/v1/query_range").form(reqParams).execute();
        String body = execute.body();
        log.info("queryRangePost -> [返回结果] {}", body);
        return body;
    }

    /**
     * 格式化时间秒->分钟、小时、天、周、年
     * @param seconds
     * @return
     */
    public static String formatSecondTime(Float seconds) {
        DecimalFormat df = new DecimalFormat("0.00");
        if(seconds < 60) {
            return df.format(seconds) + "秒";
        }
        Float minutes = seconds / 60;
        if(minutes < 60) {
            return df.format(minutes) + "分钟";
        }
        Float hours = minutes / 60;
        if(hours < 24) {
            return df.format(hours) + "小时";
        }
        Float days = hours / 24;
        if(days < 7) {
            return df.format(days) + "天";
        }
        Float weeks = days / 7;
        if(weeks < 52) {
            return df.format(weeks) + "周";
        }
        return df.format(weeks / 52) + "年";
    }

    /**
     * 获取主机基本信息：主机名、ip：端口、系统版本、系统类型、版本信息
     * @param ip
     * @param port
     * @return
     */
    public static MetricDTO getHostInfo(String ip, String port) {
        log.info("[查询] 获取主机名，ip={}，port={}", ip, port);
        String query = "node_uname_info{}";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            query = "node_uname_info{instance=\"" + ip + ":" + port + "\"}";
        }
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getMetric();
                }
            }
        }
        return null;
    }

    /**
     * 获取系统运行时间
     * @param ip
     * @param port
     * @return
     */
    public static Float getRunTime(String ip, String port) {
        log.info("[查询] 获取系统运行时间，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("sum(time() - node_boot_time_seconds{%s})", instance);
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }

    public static Float getMemoryTotal(String ip, String port) {
        log.info("[查询] 获取内存大小，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("sum(node_memory_MemTotal_bytes{%s})", instance);
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }

    public static Float getCpuTotal(String ip, String port) {
        log.info("[查询] 获取cpu核数，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("count(node_cpu_seconds_total{%smode=\"system\"})", instance);
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }

    /**
     * 获取 cpu 使用率
     * @param ip
     * @param port
     * @return
     */
    public static Float getCpuUsed(String ip, String port) {
        log.info("工具类getCpuUsed方法，[查询] 获取 cpu 使用率，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("1 - (avg(rate(node_cpu_seconds_total{%smode=\"idle\"}[5m])))", instance);
        Map<String, Object> reqParams = new HashMap<String, Object>();
        reqParams.put("query", query);
        reqParams.put("time", System.currentTimeMillis() / 1000);
        String body = queryPost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(CollectionUtil.isNotEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }

    /**
     * 获取 cpu 使用率
     * @param ip
     * @param port
     * @return
     */
    public static Float getCpuUsedNoTime(String ip, String port) {
        log.info("[查询] 获取 cpu 使用率，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("1 - (avg(rate(node_cpu_seconds_total{%smode=\"idle\"}[5m])))", instance);
        Map<String, Object> reqParams = new HashMap<String, Object>();
        reqParams.put("query", query);
//        reqParams.put("time", System.currentTimeMillis() / 1000);
        String body = queryPost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }


    /**
     * 获取指定时间范围内的 CPU 使用率
     * @param ip
     * @param port
     * @param hour 时间氛围
     * @param step 步长
     * @param stepUnit 步长单位
     * @return
     */
    public static List<List<Float>> getCpuUsed(String ip, String port, int hour, int step,String stepUnit) {
        log.info("[查询] 获取{}小时内 cpu 使用率，ip={}，port={}",hour,ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("1 - (avg(rate(node_cpu_seconds_total{%smode=\"idle\"}[5m])))", instance);

        Map<String, Object> reqParams = new HashMap<String, Object>();
        reqParams.put("query", query);
        Calendar calendar = Calendar.getInstance();
        long end = calendar.getTimeInMillis() / 1000;
        calendar.add(Calendar.HOUR, -hour);
        long start = calendar.getTimeInMillis() / 1000;
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step + stepUnit);
        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValues();
                }
            }
        }
        return null;
    }


    /**
     * 获取指定时间范围内的CPU使用率（精确时间范围版本）
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]
     */
    public static List<List<Float>> getCpuUsedByTimeRange(String ip, String port,
                                                          long start, long end, int step) {
        log.info("数据大屏，[查询] 获取CPU使用率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 构建PromQL查询语句（保持与原有逻辑一致）
        String query = String.format("1 - (avg(rate(node_cpu_seconds_total{%smode=\"idle\"}[5m])))", instance);

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);  // 注意这里直接使用秒数，不带单位

        String body = queryRangePost(reqParams);

        if (StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if ("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                log.info("数据大屏，CPU使用率查询结果：{}", JSON.toJSONString(resultDTOS, true));
                if (!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValues();
                }
            }
        }
        return Collections.emptyList();
    }


    /**
     * 获取指时间戳的时间范围内的 CPU 使用率
     * @param ip
     * @param port
     * @param hour 时间氛围
     * @param step 步长
     * @param stepUnit 步长单位
     * @return
     */
    public static List<List<Float>> getCpuUsed(String ip, String port, int hour, int step,String stepUnit,Calendar calendar) {
        log.info("[查询] 获取指定时间戳{},在{}小时内 cpu 使用率，ip={}，port={}",calendar.getTime(),hour,ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("1 - (avg(rate(node_cpu_seconds_total{%smode=\"idle\"}[5m])))", instance);
        Map<String, Object> reqParams = new HashMap<String, Object>();
        reqParams.put("query", query);
        long end = calendar.getTimeInMillis() / 1000;
        calendar.add(Calendar.HOUR, -hour);
        long start = calendar.getTimeInMillis() / 1000;
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step + stepUnit);
        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                log.info("resultDTOS={}", JSON.toJSONString(resultDTOS,true));
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValues();
                }
            }
        }
        return Collections.emptyList();
    }

    public static Float getDiskTotal(String ip, String port) {
        log.info("[查询] 获取硬盘大小，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("sum(node_filesystem_size_bytes{%sfstype=~\"ext.?|xfs\"})", instance);
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }

    /**
     * 获取硬盘使用率
     * @param ip
     * @param port
     * @return
     */
    public static Float getDiskUsed(String ip, String port) {
        log.info("[查询] 获取硬盘使用率，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("max((node_filesystem_size_bytes{%sfstype=~\"ext.?|xfs\"}-node_filesystem_free_bytes{%sfstype=~\"ext.?|xfs\"}) *100/(node_filesystem_avail_bytes {%sfstype=~\"ext.?|xfs\"}+(node_filesystem_size_bytes{fstype=~\"ext.?|xfs\"}-node_filesystem_free_bytes{%sfstype=~\"ext.?|xfs\"})))",
                instance, instance, instance, instance);
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }

    /**
     * 获取指定时间范围内的硬盘使用率
     *
     * @param ip
     * @param port
     * @param hour
     * @param step
     * @param stepUnit
     * @param calendar
     * @return
     */
    public static List<List<Float>> getDiskUsed(String ip, String port, int hour, int step, String stepUnit, Calendar calendar) {
        log.info("[查询] 获取硬盘使用率，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query
            = String.format("max((node_filesystem_size_bytes{%sfstype=~\"ext.?|xfs\"}-node_filesystem_free_bytes{%sfstype=~\"ext.?|xfs\"}) *100/(node_filesystem_avail_bytes {%sfstype=~\"ext.?|xfs\"}+(node_filesystem_size_bytes{fstype=~\"ext.?|xfs\"}-node_filesystem_free_bytes{%sfstype=~\"ext.?|xfs\"})))",
            instance, instance, instance, instance);
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        long end = calendar.getTimeInMillis() / 1000;
        calendar.add(Calendar.HOUR, -hour);
        long start = calendar.getTimeInMillis() / 1000;
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step + stepUnit);
        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValues();
                }
            }
        }
        return null;
    }


    /**
     * 获取指定时间范围内的磁盘使用率（精确时间范围版本）
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]
     */
    public static List<List<Float>> getDiskUsedByTimeRange(String ip, String port,
                                                           long start, long end, int step) {
        log.info("数据大屏，[查询] 获取磁盘使用率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 构建与原getDiskUsed保持一致的PromQL查询语句
        String query = String.format(
            "max((node_filesystem_size_bytes{%sfstype=~\"ext.?|xfs\"}-node_filesystem_free_bytes{%sfstype=~\"ext.?|xfs\"}) " +
                "*100/(node_filesystem_avail_bytes {%sfstype=~\"ext.?|xfs\"}+" +
                "(node_filesystem_size_bytes{fstype=~\"ext.?|xfs\"}-node_filesystem_free_bytes{%sfstype=~\"ext.?|xfs\"})))",
            instance, instance, instance, instance
        );

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);  // 直接使用秒数，与CPU/内存监控接口对齐

        String body = queryRangePost(reqParams);
        List<List<Float>> resultList = new ArrayList<>();

        if (StringUtils.isNoneBlank(body)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                    log.info("磁盘使用率查询原始结果：{}", JSON.toJSONString(resultDTOS, true));

                    // 结果处理（保持与CPU/内存监控相同逻辑）
                    if (!CollectionUtils.isEmpty(resultDTOS)) {
                        resultList = resultDTOS.get(0).getValues();
                    }
                }
            } catch (Exception e) {
                log.error("磁盘使用率查询异常 | body={} | error={}", body, e.getMessage());
            }
        }

        // 空结果保护（保持接口返回类型一致性）
        return CollectionUtil.isNotEmpty(resultList) ? resultList : Collections.emptyList();
    }


    /**
     * 获取内存使用率
     * @param ip
     * @param port
     * @return
     */
    public static Float getMemoryUsed(String ip, String port) {
        log.info("[查询] 获取内存使用率，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("(1 - (node_memory_MemAvailable_bytes{%s} / (node_memory_MemTotal_bytes{%s})))", instance, instance);
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }

    /**
     * 获取指定时间范围内的内存使用率
     * @param ip
     * @param port
     * @param hour
     * @param step
     * @param stepUnit
     * @param calendar
     * @return
     */
    public static List<List<Float>> getMemoryUsed(String ip, String port, int hour, int step,String stepUnit,Calendar calendar) {
        log.info("[查询] 获取内存使用率，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("(1 - (node_memory_MemAvailable_bytes{%s} / (node_memory_MemTotal_bytes{%s})))", instance, instance);

        Map<String, Object> reqParams = new HashMap<String, Object>();
        reqParams.put("query", query);
        long end = calendar.getTimeInMillis() / 1000;
        calendar.add(Calendar.HOUR, -hour);
        long start = calendar.getTimeInMillis() / 1000;
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step + stepUnit);

        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValues();
                }
            }
        }
        return null;
    }

    /**
     * 获取指定时间范围内的内存使用率（精确时间范围版本）
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]
     */
    public static List<List<Float>> getMemoryUsedByTimeRange(String ip, String port,
                                                          long start, long end, int step) {
        log.info("数据大屏，[查询] 获取内存使用率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 构建PromQL查询语句（保持与原有逻辑一致）
        String query = String.format("(1 - (node_memory_MemAvailable_bytes{%s} / (node_memory_MemTotal_bytes{%s})))", instance, instance);

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);  // 注意这里直接使用秒数，不带单位

        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValues();
                }
            }
        }
        return Collections.emptyList();
    }

    /**
     * 获取带宽上传字节数
     * List<Float>[1]/1024=kb
     * @param ip
     * @param port
     * @param step
     * @return
     */
    public static List<List<Float>> getNetworkUploadTotal(String ip, String port, long start, long end, int step) {
        log.info("数据大屏，[查询]下载 ，获取start:{},end:{}上传情况，ip={}，port={}", start,end, ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("rate(node_network_transmit_bytes_total{%sdevice=\"ens160\"}[2m])*8", instance);
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);


        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);
        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(CollectionUtil.isNotEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValues();
                }
            }
        }
        return Collections.emptyList();
    }


    /**
     * 获取带宽上传字节数（增强版，自动检测网卡设备）
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]（单位：bps）
     */
    public static List<List<Float>> getNetworkUploadTotal_v1(String ip, String port,
                                                          long start, long end, int step) {
        log.info("数据大屏，[查询]上传，获取时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        // 构造实例条件
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 获取活跃物理网卡设备
        String activeDevice = getActiveNetworkDevice(ip, port, start, end, step);
        log.info("getNetworkUploadTotal, 活跃网卡设备：{}", activeDevice);
        if (StringUtils.isBlank(activeDevice)) {
            log.warn("未检测到有效物理网卡设备");
            return Collections.emptyList();
        }

        /* 构建PromQL查询语句：
         * 1. 使用rate计算2分钟时间窗口的字节传输速率（单位：bytes/s）
         * 2. 乘以8转换为比特率（单位：bits/s）
         * 3. 使用自动检测的网卡设备
         */
        String query = String.format("rate(node_network_transmit_bytes_total{%sdevice=\"%s\"}[2m])*8",
            instance, activeDevice);

        // 构造查询参数
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);

        // 执行查询并处理结果
        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                    if(CollectionUtil.isNotEmpty(resultDTOS)) {
                        return resultDTOS.get(0).getValues();
                    }
                }
            } catch (Exception e) {
                log.error("处理网络上传数据异常", e);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 获取指定时间范围内的网络上传带宽使用率（增强版）
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]（单位：bps）
     */
    public static List<List<Float>> getNetworkUploadUsageByTimeRange_v1(String ip, String port,
                                                                     long start, long end, int step) {
        log.info("数据大屏，[查询]上传带宽使用率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        // 使用改造后的getNetworkUploadTotal方法
        return getNetworkUploadTotal(ip, port, start, end, step);
    }


    /**
     * 查询指定实例在时间范围内的网络接收总速率数据（单位：bit/s）
     *
     * @param ip    目标实例IP地址（可选，与port共同生效）
     * @param port  目标实例端口号（可选，与ip共同生效）
     * @param start 查询起始时间戳（秒级）
     * @param end   查询结束时间戳（秒级）
     * @param step  查询步长时间间隔（秒级）
     * @return 时间序列数据集合，每个元素为包含两个浮点数的列表：[时间戳, 网络接收速率值]
     *         若无数据返回空集合
     */
    public static List<List<Float>> getNetworkReceiveTotal(String ip, String port, long start, long end, int step) {
        log.info("数据大屏，[查询]下载 ，获取start:{},end:{}上传情况，ip={}，port={}", start,end, ip, port);

        // 构造Prometheus查询实例条件（当IP和端口均非空时生效）
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        /* 构建PromQL查询语句：
         * 1. 使用rate计算2分钟时间窗口的字节接收速率（单位：bytes/s）
         * 2. 乘以8转换为比特率（单位：bits/s）
         * 3. 固定过滤网卡设备为ens160
         */
        String query = String.format("rate(node_network_receive_bytes_total{%sdevice=\"ens160\"}[2m])*8", instance);

        // 构造Prometheus range query请求参数
        Map<String, Object> reqParams = new HashMap<String, Object>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);

        // 发送HTTP请求到Prometheus API并处理响应
        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            List<ResultDTO> resultDTOS = respDTO.getData().getResult();

            // 提取第一个结果集的时间序列数据（假设单个设备查询只会返回一个结果集）
            if(CollectionUtil.isNotEmpty(resultDTOS)) {
                return resultDTOS.get(0).getValues();
            }
        }
        return Collections.emptyList();
    }


    /**
     * 增强版本
     * 查询指定实例在时间范围内的网络接收总速率数据（单位：bit/s）
     *
     * @param ip    目标实例IP地址（可选，与port共同生效）
     * @param port  目标实例端口号（可选，与ip共同生效）
     * @param start 查询起始时间戳（秒级）
     * @param end   查询结束时间戳（秒级）
     * @param step  查询步长时间间隔（秒级）
     * @return 时间序列数据集合，每个元素为包含两个浮点数的列表：[时间戳, 网络接收速率值]
     *         若无数据返回空集合
     */
    public static List<List<Float>> getNetworkReceiveTotal_v1(String ip, String port, long start, long end, int step) {
        log.info("数据大屏，[查询]下载，获取时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        // 构造Prometheus查询实例条件（当IP和端口均非空时生效）
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 获取活跃物理网卡设备（自动检测）
        String activeDevice = getActiveNetworkDevice(ip, port, start, end, step);
        log.info("getNetworkReceiveTotal_v1,活跃网卡设备：{}", activeDevice);
        if (StringUtils.isBlank(activeDevice)) {
            log.warn("未检测到有效物理网卡设备");
            return Collections.emptyList();
        }

        /* 构建PromQL查询语句：
         * 1. 使用rate计算2分钟时间窗口的字节接收速率（单位：bytes/s）
         * 2. 乘以8转换为比特率（单位：bits/s）
         * 3. 使用自动检测的网卡设备
         */
        String query = String.format("rate(node_network_receive_bytes_total{%sdevice=\"%s\"}[2m])*8",
            instance, activeDevice);

        // 构造Prometheus range query请求参数
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);

        // 发送HTTP请求到Prometheus API并处理响应
        String body = queryRangePost(reqParams);
        if(StringUtils.isNoneBlank(body)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();

                    // 提取第一个结果集的时间序列数据
                    if(CollectionUtil.isNotEmpty(resultDTOS)) {
                        return resultDTOS.get(0).getValues();
                    }
                }
            } catch (Exception e) {
                log.error("处理Prometheus响应异常", e);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 自动检测活跃物理网卡（带缓存机制和时间范围查询）
     *
     * @param ip 目标IP地址
     * @param port 目标端口号
     * @param start 开始时间戳（秒级）
     * @param end 结束时间戳（秒级）
     * @param step 查询步长（秒级）
     * @return 活跃网卡设备名称
     */
    private static synchronized String getActiveNetworkDevice(String ip, String port,
                                                              long start, long end, int step) {
        // 如果已有缓存且未超时，直接返回
        if (!NETWORK_DEVICE.isEmpty() &&
            System.currentTimeMillis() - lastDeviceDetectionTime < DEVICE_CACHE_TIMEOUT_MS) {
            return NETWORK_DEVICE;
        }

        String instance = StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)
            ? "instance=\"" + ip + ":" + port + "\"" : "";

        try {
            // 构建PromQL查询语句
            String query = String.format("node_network_receive_bytes_total{%s}", instance);

            // 构造时间范围查询参数
            Map<String, Object> reqParams = new HashMap<>();
            reqParams.put("query", query);
            reqParams.put("start", start);
            reqParams.put("end", end);
            reqParams.put("step", step);

            // 发送HTTP请求到Prometheus API
            String body = queryRangePost(reqParams);

            if (StringUtils.isNoneBlank(body)) {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    // 从结果中提取活跃网卡设备
                    Optional<String> activeDevice = respDTO.getData().getResult().stream()
                        .map(ResultDTO::getMetric)
                        .map(metric -> (String) metric.get("device"))
                        .filter(device -> device != null &&
                            VIRTUAL_DEVICE_PREFIXES.stream().noneMatch(device::startsWith))
                        .findFirst();

                    if (activeDevice.isPresent()) {
                        NETWORK_DEVICE = activeDevice.get();
                        lastDeviceDetectionTime = System.currentTimeMillis();
                        log.info("自动检测到活跃网卡设备: {}", NETWORK_DEVICE);
                        return NETWORK_DEVICE;
                    }
                }
            }
        } catch (Exception e) {
            log.error("自动获取网卡设备异常", e);
        }

        // 失败时使用默认策略
        if (NETWORK_DEVICE.isEmpty()) {
            NETWORK_DEVICE = "eth0"; // 最终回退值
            log.warn("使用默认网卡设备: {}", NETWORK_DEVICE);
        }
        return NETWORK_DEVICE;
    }

    /**
     * 获取总5分钟负载
     * @param ip
     * @param port
     * @return
     */
    public static Float getLoadTotal(String ip, String port) {
        log.info("[查询] 获取总5分钟负载，ip={}，port={}", ip, port);
        String instance = "";
        if(StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }
        String query = String.format("sum(node_load5{%s})", instance);
        String body = post(query);
        if(StringUtils.isNoneBlank(body)) {
            RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
            if("success".equals(respDTO.getStatus())) {
                List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                if(!CollectionUtils.isEmpty(resultDTOS)) {
                    return resultDTOS.get(0).getValue().get(1);
                }
            }
        }
        return 0f;
    }



    /**
     * 根据 ip、port 获取系统性能指标
     * @param ip
     * @param port
     * @return
     */
    public static SystemInfoDTO getSystemInfo(String ip, String port) {
        SystemInfoDTO systemInfoDTO = new SystemInfoDTO();
        DecimalFormat df = new DecimalFormat("0.00");

        systemInfoDTO.setCpu(getCpuTotal(ip, port).intValue());
        systemInfoDTO.setMemory(String.valueOf((int) Math.ceil(getMemoryTotal(ip, port)/1024/1024/1024)));
        systemInfoDTO.setDisk(String.valueOf((int) Math.ceil(getDiskTotal(ip, port)/1024/1024/1024)));
        return systemInfoDTO;
    }

    /**
     * 计算替换 instance 的个数
     * @param str pql语句
     * @return
     */
    public static int getStringFormatByInstanceCount(String str){
        // 使用正则表达式匹配 %s
        String regex = "%s";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    /**
     * 获取指定时间范围内的网络带宽使用率（上传）
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]（单位：bps）
     */
    public static List<List<Float>> getNetworkUploadUsageByTimeRange(String ip, String port,
                                                                   long start, long end, int step) {
        log.info("数据大屏，[查询] 获取网络上传带宽使用率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 构建PromQL查询语句（保持与原有网络监控逻辑一致）
        String query = String.format("rate(node_network_transmit_bytes_total{%sdevice=\"ens160\"}[2m])*8", instance);

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);

        return executeNetworkQuery(reqParams);
    }

    /**
     * 获取指定时间范围内的网络带宽使用率（下载）
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]（单位：bps）
     */
    public static List<List<Float>> getNetworkReceiveUsageByTimeRange(String ip, String port,
                                                                    long start, long end, int step) {
        log.info("数据大屏，[查询] 获取网络下载带宽使用率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 构建PromQL查询语句（保持与原有网络监控逻辑一致）
        String query = String.format("rate(node_network_receive_bytes_total{%sdevice=\"ens160\"}[2m])*8", instance);

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);

        return executeNetworkQuery(reqParams);
    }

    /**
     * 执行网络查询公共方法
     * @param reqParams 请求参数
     * @return 时间序列数据
     */
    private static List<List<Float>> executeNetworkQuery(Map<String, Object> reqParams) {
        String body = queryRangePost(reqParams);
        List<List<Float>> resultList = new ArrayList<>();

        if (StringUtils.isNoneBlank(body)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                    log.info("网络带宽使用率查询原始结果：{}", JSON.toJSONString(resultDTOS, true));

                    if (!CollectionUtils.isEmpty(resultDTOS)) {
                        resultList = resultDTOS.get(0).getValues();
                    }
                }
            } catch (Exception e) {
                log.error("网络带宽查询异常 | body={} | error={}", body, e.getMessage());
            }
        }
        return CollectionUtil.isNotEmpty(resultList) ? resultList : Collections.emptyList();
    }

    /**
     * 获取活跃磁盘设备 - 增强版
     * 智能检测多种类型的磁盘设备并选择IO活跃度最高的设备
     *
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 活跃磁盘设备名称
     */
    private static String getActiveDiskDevice(String ip, String port, long start, long end, int step) {
        // 如果缓存有效，直接返回
        if (StringUtils.isNotBlank(DISK_DEVICE) &&
            System.currentTimeMillis() - lastDiskDetectionTime < DISK_CACHE_TIMEOUT_MS) {
            log.info("使用缓存的磁盘设备: {}", DISK_DEVICE);
            return DISK_DEVICE;
        }

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 第一步：获取所有可用磁盘设备列表
        String listQuery = String.format("node_disk_reads_completed_total{%s}", instance);
        Map<String, Object> listParams = new HashMap<>();
        listParams.put("query", listQuery);
        listParams.put("time", end);

        String listBody = queryPost(listParams);
        Set<String> availableDevices = new HashSet<>();

        if (StringUtils.isNoneBlank(listBody)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(listBody, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                    if (!CollectionUtils.isEmpty(resultDTOS)) {
                        // 收集所有物理磁盘设备
                        for (ResultDTO result : resultDTOS) {
                            MetricDTO metric = result.getMetric();
                            if (metric != null) {
                                Object deviceObj = metric.get("device");
                                if (deviceObj != null) {
                                    String device = deviceObj.toString();
                                    // 过滤掉分区（通常包含数字）和非物理设备
                                    if (isPhysicalDisk(device)) {
                                        availableDevices.add(device);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取磁盘设备列表异常：{}", e.getMessage(), e);
            }
        }

        log.info("检测到的磁盘设备列表: {}", availableDevices);

        // 如果没有找到任何设备，尝试常见设备名称
        if (availableDevices.isEmpty()) {
            log.warn("未检测到任何磁盘设备，将尝试常见设备名称");
            availableDevices.addAll(Arrays.asList("sda", "vda", "xvda", "nvme0n1", "dm-0"));
        }

        // 第二步：查询IO活跃度最高的设备
        if (availableDevices.size() == 1) {
            // 只有一个设备，直接使用
            DISK_DEVICE = availableDevices.iterator().next();
            lastDiskDetectionTime = System.currentTimeMillis();
            log.info("只有一个磁盘设备可用: {}", DISK_DEVICE);
            return DISK_DEVICE;
        }

        // 构建查询，比较所有设备的IO活跃度
        StringBuilder queryBuilder = new StringBuilder("topk(1, max by (device) (");
        queryBuilder.append("rate(node_disk_reads_completed_total{").append(instance).append("device=~\"");
        queryBuilder.append(String.join("|", availableDevices));
        queryBuilder.append("\"}[5m]) + ");
        queryBuilder.append("rate(node_disk_writes_completed_total{").append(instance).append("device=~\"");
        queryBuilder.append(String.join("|", availableDevices));
        queryBuilder.append("\"}[5m])))");

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", queryBuilder.toString());
        reqParams.put("time", end);

        String body = queryPost(reqParams);
        if (StringUtils.isNoneBlank(body)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                    if (!CollectionUtils.isEmpty(resultDTOS)) {
                        MetricDTO metric = resultDTOS.get(0).getMetric();
                        if (metric != null) {
                            Object deviceObj = metric.get("device");
                            if (deviceObj != null) {
                                DISK_DEVICE = deviceObj.toString();
                                lastDiskDetectionTime = System.currentTimeMillis();
                                log.info("检测到最活跃的磁盘设备: {}", DISK_DEVICE);
                                return DISK_DEVICE;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取活跃磁盘设备异常：{}", e.getMessage(), e);
            }
        }

        // 如果仍然没有找到活跃设备，使用第一个可用设备
        if (!availableDevices.isEmpty()) {
            DISK_DEVICE = availableDevices.iterator().next();
            lastDiskDetectionTime = System.currentTimeMillis();
            log.warn("未能确定最活跃设备，使用第一个可用设备: {}", DISK_DEVICE);
            return DISK_DEVICE;
        }

        // 最终回退到默认设备
        DISK_DEVICE = "sda";
        lastDiskDetectionTime = System.currentTimeMillis();
        log.warn("未检测到任何可用设备，使用默认设备: {}", DISK_DEVICE);
        return DISK_DEVICE;
    }

    /**
     * 判断是否为物理磁盘设备（而非分区）
     */
    private static boolean isPhysicalDisk(String device) {
        // 检查是否以常见磁盘前缀开头
        boolean hasValidPrefix = COMMON_DISK_PREFIXES.stream()
            .anyMatch(device::startsWith);

        // 排除明显的分区（通常包含数字，如sda1, sda2）
        // 但保留完整设备名如nvme0n1, dm-0
        boolean isPartition = device.matches("^[a-z]+\\d+$");

        return hasValidPrefix && !isPartition;
    }

    /**
     * 获取指定时间范围内的磁盘IO使用率
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 时间序列数据列表 [[时间戳, 值], ...]（单位：IOPS）
     */
    public static List<List<Float>> getIOUsageByTimeRange(String ip, String port,
                                                          long start, long end, int step) {
        log.info("数据大屏，[查询] 获取磁盘IO使用率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 获取活跃磁盘设备
        String activeDisk = getActiveDiskDevice(ip, port, start, end, step);
        log.info("getIOUsageByTimeRange, 活跃磁盘设备：{}", activeDisk);

        if (StringUtils.isBlank(activeDisk)) {
            log.warn("未检测到有效磁盘设备，使用默认设备sda");
            activeDisk = "sda"; // 默认使用sda设备
        }

        // 构建PromQL查询语句：磁盘IO使用率 = 读写操作总数/秒
        // 使用rate函数计算每秒读写操作数（IOPS）
        String query = String.format(
            "sum(rate(node_disk_reads_completed_total{%sdevice=\"%s\"}[2m]) + " +
                "rate(node_disk_writes_completed_total{%sdevice=\"%s\"}[2m]))",
            instance, activeDisk, instance, activeDisk);

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("query", query);
        reqParams.put("start", start);
        reqParams.put("end", end);
        reqParams.put("step", step);  // 直接使用秒数，不带单位

        String body = queryRangePost(reqParams);
        if (StringUtils.isNoneBlank(body)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                    log.info("数据大屏，IO使用率查询结果：{}", JSON.toJSONString(resultDTOS, true));
                    if (!CollectionUtils.isEmpty(resultDTOS)) {
                        return resultDTOS.get(0).getValues();
                    } else {
                        log.warn("IO使用率查询返回空结果集");
                    }
                } else {
                    log.warn("IO使用率查询失败，状态：{}", respDTO.getStatus());
                }
            } catch (Exception e) {
                log.error("IO使用率查询异常：{}", e.getMessage(), e);
            }
        } else {
            log.warn("IO使用率查询返回为空");
        }

        return Collections.emptyList();
    }

    /**
     * 获取指定时间范围内的磁盘IO读写速率
     * @param ip 服务器IP
     * @param port 端口号
     * @param start 开始时间戳（秒）
     * @param end 结束时间戳（秒）
     * @param step 采样间隔（秒）
     * @return 包含读写速率的Map，键为"read"和"write"，值为时间序列数据
     */
    public static Map<String, List<List<Float>>> getDiskIOStatsByTimeRange(String ip, String port,
                                                                           long start, long end, int step) {
        log.info("数据大屏，[查询] 获取磁盘IO读写速率，时间范围：{}-{}，ip={}，port={}", start, end, ip, port);

        String instance = "";
        if (StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(port)) {
            instance = "instance=\"" + ip + ":" + port + "\", ";
        }

        // 获取活跃磁盘设备
        String activeDisk = getActiveDiskDevice(ip, port, start, end, step);
        log.info("getDiskIOStatsByTimeRange, 活跃磁盘设备：{}", activeDisk);

        if (StringUtils.isBlank(activeDisk)) {
            log.warn("未检测到有效磁盘设备，使用默认设备sda");
            activeDisk = "sda"; // 默认使用sda设备
        }

        Map<String, List<List<Float>>> result = new HashMap<>();

        // 查询读取速率（bytes/s）
        String readQuery = String.format(
            "rate(node_disk_read_bytes_total{%sdevice=\"%s\"}[2m])",
            instance, activeDisk);

        // 查询写入速率（bytes/s）
        String writeQuery = String.format(
            "rate(node_disk_written_bytes_total{%sdevice=\"%s\"}[2m])",
            instance, activeDisk);

        // 执行读取速率查询
        Map<String, Object> readParams = new HashMap<>();
        readParams.put("query", readQuery);
        readParams.put("start", start);
        readParams.put("end", end);
        readParams.put("step", step);

        List<List<Float>> readData = executeRangeQuery(readParams);
        result.put("read", readData);

        // 执行写入速率查询
        Map<String, Object> writeParams = new HashMap<>();
        writeParams.put("query", writeQuery);
        writeParams.put("start", start);
        writeParams.put("end", end);
        writeParams.put("step", step);

        List<List<Float>> writeData = executeRangeQuery(writeParams);
        result.put("write", writeData);

        return result;
    }

    /**
     * 执行范围查询并返回结果
     * 提取公共查询逻辑，减少代码重复
     */
    private static List<List<Float>> executeRangeQuery(Map<String, Object> params) {
        String body = queryRangePost(params);
        if (StringUtils.isNoneBlank(body)) {
            try {
                RespDTO respDTO = JSONUtil.toBean(body, RespDTO.class);
                if ("success".equals(respDTO.getStatus())) {
                    List<ResultDTO> resultDTOS = respDTO.getData().getResult();
                    if (!CollectionUtils.isEmpty(resultDTOS)) {
                        return resultDTOS.get(0).getValues();
                    }
                }
            } catch (Exception e) {
                log.error("执行范围查询异常：{}, 参数: {}", e.getMessage(), params, e);
            }
        }
        return Collections.emptyList();
    }

    public static void main(String[] args) {
        // 测试服务器配置
        String ip = "*************";
        String port = "9100";

        // 测试CPU使用率（保留原有代码）
//        long start = LocalDate.now().atStartOfDay().toEpochSecond(ZoneOffset.UTC);
//        long end = start + 86399;  // 23:59:59
//        List<List<Float>> todayData = getCpuUsedByTimeRange(ip, port, start, end, 300);  // 5分钟间隔
//        System.out.println("todayData: " + todayData);

        // 获取最近7天数据（保留原有代码）
//        long weekStart = LocalDate.now().minusDays(6).atStartOfDay().toEpochSecond(ZoneOffset.UTC);
//        long weekEnd = LocalDate.now().atTime(23,59,59).toEpochSecond(ZoneOffset.UTC);
//        List<List<Float>> weekData = getCpuUsedByTimeRange(ip, port, weekStart, weekEnd, 86400);  // 24小时间隔

        // 测试IO使用率 - 获取最近1小时的IO使用情况
//        System.out.println("\n===== 测试磁盘IO使用率 =====");
//        // 计算最近1小时的时间范围
//        long ioStart = System.currentTimeMillis()/1000 - 3600; // 1小时前
//        long ioEnd = System.currentTimeMillis()/1000;          // 当前时间
//        int ioStep = 60;                                       // 1分钟间隔
//
//        // 1. 先测试获取活跃磁盘设备
//        String activeDisk = getActiveDiskDevice(ip, port, ioStart, ioEnd, ioStep);
//        System.out.println("检测到的活跃磁盘设备: " + activeDisk);
//
//        // 2. 测试获取IO使用率
//        List<List<Float>> ioUsage = getIOUsageByTimeRange(ip, port, ioStart, ioEnd, ioStep);
//        System.out.println("IO使用率(IOPS): " + (ioUsage.isEmpty() ? "无数据" : ioUsage.size() + "个数据点"));
//        // 打印前5个数据点示例
//        if (!ioUsage.isEmpty()) {
//            System.out.println("IO使用率前5个数据点示例:");
//            for (int i = 0; i < Math.min(5, ioUsage.size()); i++) {
//                List<Float> point = ioUsage.get(i);
//                if (point.size() >= 2) {
//                    // 格式化时间戳为可读格式
//                    Date date = new Date(point.get(0).longValue() * 1000);
//                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                    System.out.println("  " + sdf.format(date) + " : " + point.get(1) + " IOPS");
//                }
//            }
//        }
//
//        // 3. 测试获取IO读写速率
//        System.out.println("\n===== 测试磁盘IO读写速率 =====");
//        Map<String, List<List<Float>>> ioStats = getDiskIOStatsByTimeRange(ip, port, ioStart, ioEnd, ioStep);
//
//        // 打印读取速率
//        List<List<Float>> readStats = ioStats.get("read");
//        System.out.println("读取速率: " + (readStats.isEmpty() ? "无数据" : readStats.size() + "个数据点"));
//        if (!readStats.isEmpty()) {
//            System.out.println("读取速率前5个数据点示例:");
//            for (int i = 0; i < Math.min(5, readStats.size()); i++) {
//                List<Float> point = readStats.get(i);
//                if (point.size() >= 2) {
//                    Date date = new Date(point.get(0).longValue() * 1000);
//                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                    // 转换为KB/s显示更直观
//                    float kbPerSec = point.get(1) / 1024;
//                    System.out.println("  " + sdf.format(date) + " : " + String.format("%.2f", kbPerSec) + " KB/s");
//                }
//            }
//        }
//
//        // 打印写入速率
//        List<List<Float>> writeStats = ioStats.get("write");
//        System.out.println("写入速率: " + (writeStats.isEmpty() ? "无数据" : writeStats.size() + "个数据点"));
//        if (!writeStats.isEmpty()) {
//            System.out.println("写入速率前5个数据点示例:");
//            for (int i = 0; i < Math.min(5, writeStats.size()); i++) {
//                List<Float> point = writeStats.get(i);
//                if (point.size() >= 2) {
//                    Date date = new Date(point.get(0).longValue() * 1000);
//                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                    // 转换为KB/s显示更直观
//                    float kbPerSec = point.get(1) / 1024;
//                    System.out.println("  " + sdf.format(date) + " : " + String.format("%.2f", kbPerSec) + " KB/s");
//                }
//            }
//        }

        // 测试网络带宽上传下载 - 获取最近1小时的网络带宽使用情况
        System.out.println("\n===== 测试网络带宽上传下载 =====");
        // 使用与IO测试相同的时间范围
        long networkStart = System.currentTimeMillis()/1000 - 3600; // 1小时前
        long networkEnd = System.currentTimeMillis()/1000;          // 当前时间
        int networkStep = 60;                                       // 1分钟间隔

        // 1. 先测试获取活跃网卡设备
        String activeNetworkDevice = getActiveNetworkDevice(ip, port, networkStart, networkEnd, networkStep);
        System.out.println("检测到的活跃网卡设备: " + activeNetworkDevice);

        // 2. 测试获取网络上传带宽使用率
        List<List<Float>> uploadUsage = getNetworkUploadUsageByTimeRange(ip, port, networkStart, networkEnd, networkStep);
        System.out.println("网络上传带宽使用率: " + (uploadUsage.isEmpty() ? "无数据" : uploadUsage.size() + "个数据点"));
        // 打印前5个数据点示例
        if (!uploadUsage.isEmpty()) {
            System.out.println("网络上传带宽前5个数据点示例:");
            for (int i = 0; i < Math.min(5, uploadUsage.size()); i++) {
                List<Float> point = uploadUsage.get(i);
                if (point.size() >= 2) {
                    // 格式化时间戳为可读格式
                    Date date = new Date(point.get(0).longValue() * 1000);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    // 转换为Mbps显示更直观 (bps -> Mbps)
                    float mbps = point.get(1) / 1024 / 1024;
                    System.out.println("  " + sdf.format(date) + " : " + String.format("%.2f", mbps) + " Mbps");
                }
            }
        }

        // 3. 测试获取网络下载带宽使用率
        List<List<Float>> downloadUsage = getNetworkReceiveUsageByTimeRange(ip, port, networkStart, networkEnd, networkStep);
        System.out.println("网络下载带宽使用率: " + (downloadUsage.isEmpty() ? "无数据" : downloadUsage.size() + "个数据点"));
        // 打印前5个数据点示例
        if (!downloadUsage.isEmpty()) {
            System.out.println("网络下载带宽前5个数据点示例:");
            for (int i = 0; i < Math.min(5, downloadUsage.size()); i++) {
                List<Float> point = downloadUsage.get(i);
                if (point.size() >= 2) {
                    // 格式化时间戳为可读格式
                    Date date = new Date(point.get(0).longValue() * 1000);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    // 转换为Mbps显示更直观 (bps -> Mbps)
                    float mbps = point.get(1) / 1024 / 1024;
                    System.out.println("  " + sdf.format(date) + " : " + String.format("%.2f", mbps) + " Mbps");
                }
            }
        }

        // 4. 测试获取网络上传总量（使用增强版方法）
        System.out.println("\n===== 测试网络上传下载总量（增强版） =====");
        List<List<Float>> uploadTotal = getNetworkUploadTotal_v1(ip, port, networkStart, networkEnd, networkStep);
        System.out.println("网络上传总量(增强版): " + (uploadTotal.isEmpty() ? "无数据" : uploadTotal.size() + "个数据点"));
        if (!uploadTotal.isEmpty()) {
            System.out.println("网络上传总量前5个数据点示例:");
            for (int i = 0; i < Math.min(5, uploadTotal.size()); i++) {
                List<Float> point = uploadTotal.get(i);
                if (point.size() >= 2) {
                    Date date = new Date(point.get(0).longValue() * 1000);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    // 转换为Mbps显示更直观 (bps -> Mbps)
                    float mbps = point.get(1) / 1024 / 1024;
                    System.out.println("  " + sdf.format(date) + " : " + String.format("%.2f", mbps) + " Mbps");
                }
            }
        }

        // 5. 测试获取网络下载总量（使用增强版方法）
        List<List<Float>> downloadTotal = getNetworkReceiveTotal_v1(ip, port, networkStart, networkEnd, networkStep);
        System.out.println("网络下载总量(增强版): " + (downloadTotal.isEmpty() ? "无数据" : downloadTotal.size() + "个数据点"));
        if (!downloadTotal.isEmpty()) {
            System.out.println("网络下载总量前5个数据点示例:");
            for (int i = 0; i < Math.min(5, downloadTotal.size()); i++) {
                List<Float> point = downloadTotal.get(i);
                if (point.size() >= 2) {
                    Date date = new Date(point.get(0).longValue() * 1000);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    // 转换为Mbps显示更直观 (bps -> Mbps)
                    float mbps = point.get(1) / 1024 / 1024;
                    System.out.println("  " + sdf.format(date) + " : " + String.format("%.2f", mbps) + " Mbps");
                }
            }
        }
    }

}
